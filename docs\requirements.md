# 学生寝室分配系统项目需求

## 1. 系统概述
学生寝室分配系统旨在为学生提供便捷的寝室入住申请渠道，并为管理员提供高效的申请审核与寝室分配管理工具。系统通过智能化手段，优化宿舍分配流程，提升管理效率，同时满足学生的个性化需求。

## 2. 功能模块

### 2.1 学生端功能

#### 2.1.1 用户注册与登录
- 学生使用学号、身份证号等信息注册账号。
- 提供密码找回功能，确保账号安全。
- 学生通过密码登录系统。

#### 2.1.2 寝室申请
- 学生提交寝室入住申请，填写个人信息（姓名、学号、年级、专业、辅导员、手机号等）。
- 学生选择期望的校区（如A校区、B校区等）。
- 学生选择楼层偏好（高楼层或低楼层）。
- 学生选择期望的楼栋（只能看见对应校区范围内的楼栋），系统自动根据楼栋分配寝室类型（由后台管理员预先规定）。
- 学生可以发起“喊话”功能，标注合住室友的要求（如性别、生活习惯、作息时间等），符合要求的学生可以快速应答组队，人齐后停止喊话。

#### 2.1.3 申请进度查询
- 学生可以随时查看自己的申请状态（如“已提交”“审核中”“已分配”“已拒绝”等）。
- 系统通过短信或邮件通知学生申请结果。

#### 2.1.4 信息修改
- 在申请未完成分配前，学生可以修改个人信息或申请条件（包括校区、楼层偏好、楼栋选择等）。

### 2.2 管理员端功能

#### 2.2.1 用户管理
- 管理学生账号，包括添加、删除、修改学生信息。
- 管理管理员账号，设置不同权限等级。

#### 2.2.2 寝室信息管理
- 添加、删除、修改寝室信息，包括寝室号、房间类型、床位数量、楼层、设施等。
- 明确标注寝室所属校区和楼栋。
- 标记寝室的使用状态（空闲、已分配、维修中等）。

#### 2.2.3 申请审核与分配
- 查看学生提交的申请列表，按照申请时间、年级、专业、校区、楼层偏好等条件排序。
- 审核申请，通过或拒绝申请，并给出理由。
- 根据寝室空闲情况和学生申请条件（包括校区、楼层偏好、楼栋选择等）进行分配，支持手动分配和自动分配（按规则）。
- 在自动分配规则中，优先考虑学生选择的校区和楼层偏好，以及楼栋分配的寝室类型。

#### 2.2.4 统计与报表
- 统计各校区空闲寝室数量、已分配寝室数量、待分配申请数量等。
- 生成报表，如按校区、年级、专业、寝室类型等分类的分配情况报表。

#### 2.2.5 系统设置
- 设置申请开放时间、申请截止时间等。
- 配置自动分配规则，如优先分配高年级学生、优先分配同一专业的学生、优先满足校区和楼层偏好等。
- 预先规定各楼栋对应的寝室类型。

### 2.3 系统通用功能

#### 2.3.1 通知系统
- 系统自动向学生发送申请状态更新通知（短信、邮件或站内信）。
- 管理员可以发布公告，通知学生重要信息（如申请截止日期变更、分配规则调整等）。

#### 2.3.2 数据备份与恢复
- 定期备份系统数据，防止数据丢失。
- 提供数据恢复功能，确保系统稳定性。

#### 2.3.3 安全与权限管理
- 确保学生和管理员数据的安全性，防止信息泄露。
- 根据用户角色分配不同的操作权限，确保系统安全运行。

---

## 3. 系统特点
- **多校区支持**：系统支持多个校区的寝室管理，学生可以根据个人需求选择校区。
- **楼层偏好**：学生可以选择高楼层或低楼层，系统将优先满足学生的楼层偏好。
- **自动分配与手动分配**：系统支持自动分配和手动分配两种模式，管理员可以根据实际情况灵活操作。
- **通知与提醒**：系统通过短信、邮件或站内信等方式及时通知学生申请状态和重要信息。
- **喊话功能**：学生可以通过“喊话”功能快速找到合住室友，人齐后自动停止喊话。
- **性别筛选**：单间宿舍中只允许同性别室友，管理员可设置以楼为单位、以层为单位、以寝室为单位的性别限制。
- **智能化分配**：引入基于学生个性的分配算法，如通过问卷调查了解学生的作息时间、兴趣爱好等，根据匹配度进行室友分配。